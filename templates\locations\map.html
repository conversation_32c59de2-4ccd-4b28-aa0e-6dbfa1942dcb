{% extends 'base.html' %}
{% load static %}

{% block external_css %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}

{% block compressed_css %}
<style>
    /* Map container */
    #map {
        height: 600px;
        width: 100%;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        background: #198754 !important;
        overflow: hidden;
    }

    .leaflet-container {
        background: #198754 !important;
    }

    .leaflet-tile-container {
        background-color: #198754 !important;
    }

    .leaflet-tile-pane {
        display: none !important; /* Hide tile layer completely */
    }

    .leaflet-control-attribution {
        display: none;
    }

    .marker {
        position: absolute;
        width: 25px;
        height: 41px;
        margin-left: -12px;
        margin-top: -41px;
        cursor: pointer;
        z-index: 2;
    }

    .marker-popup {
        position: absolute;
        background: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        z-index: 3;
        display: none;
    }

    .marker-popup.active {
        display: block;
    }

    /* Nearby lakes section */
    .nearby-lakes {
        margin-top: 2rem;
        padding: 1rem;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .nearby-lake-card {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .nearby-lake-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .nearby-lake-card .distance {
        color: #198653;
        font-weight: 600;
    }

    /* Location permission modal */
    .location-modal .modal-content {
        border-radius: 1rem;
    }

    .location-modal .modal-header {
        border-bottom: none;
        padding-bottom: 0;
    }

    .location-modal .modal-body {
        padding: 2rem;
        text-align: center;
    }

    .location-modal .modal-footer {
        border-top: none;
        justify-content: center;
        padding-top: 0;
    }

    .location-modal .location-icon {
        font-size: 3rem;
        color: #198653;
        margin-bottom: 1rem;
    }

    .lake-popup {
        max-width: 350px;
    }

    .lake-popup img {
        width: 100%;
        height: 180px;
        object-fit: cover;
        border-radius: 8px;
        margin-bottom: 15px;
    }

    .lake-popup h5 {
        margin: 0 0 10px 0;
        color: var(--dark-color);
        font-size: 1.2rem;
        font-weight: 600;
    }

    .lake-popup p {
        margin: 8px 0;
        color: #666;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .lake-popup i {
        color: var(--primary-color);
        width: 20px;
        text-align: center;
    }

    .lake-popup .facilities {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin: 10px 0;
    }

    .lake-popup .facility {
        background-color: #f8f9fa;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .lake-popup .price {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 1.1rem;
        margin: 12px 0;
    }

    .lake-popup .btn {
        margin-top: 15px;
        width: 100%;
    }

    /* Rating stars in popup */
    .lake-popup .rating-stars {
        display: flex;
        align-items: center;
        gap: 2px;
        margin: 8px 0;
    }

    .lake-popup .rating-stars i {
        color: #ffc107;
        font-size: 1rem;
        width: auto;
    }

    .lake-popup .rating-info {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 8px 0;
        color: #666;
    }

    .lake-popup .rating-value {
        font-weight: 600;
        color: #ffc107;
    }

    /* User location pin marker */
    .user-location-pin {
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        z-index: 1000;
    }

    .user-location-pin svg {
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h1 class="mb-3">Bălți de pescuit</h1>
            <p class="lead">Descoperă cele mai bune locuri de pescuit din România</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-3 mb-4">
            <!-- Filters -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title mb-3">Filtrează</h5>
                    <form id="filterForm">
                        <!-- County Filter -->
                        <div class="mb-3">
                            <label for="county" class="form-label">Județ</label>
                            <select class="form-select" id="county" name="county">
                                <option value="">Toate județele</option>
                                {% for county in counties %}
                                <option value="{{ county.id }}">{{ county.name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Fish Types Filter -->
                        <div class="mb-3">
                            <label class="form-label">Specii de pești</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="crap" id="crapCheck">
                                <label class="form-check-label" for="crapCheck">Crap</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="caras" id="carasCheck">
                                <label class="form-check-label" for="carasCheck">Caras</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="stiuca" id="stiucaCheck">
                                <label class="form-check-label" for="stiucaCheck">Știucă</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="salau" id="salauCheck">
                                <label class="form-check-label" for="salauCheck">Șalău</label>
                            </div>
                        </div>

                        <!-- Price Range Filter -->
                        <div class="mb-3">
                            <label for="priceRange" class="form-label">Preț maxim/zi: <span id="priceValue">100</span> Lei</label>
                            <input type="range" class="form-range" id="priceRange" min="0" max="200" step="10" value="100">
                        </div>

                        <!-- Rating Filter -->
                        <div class="mb-3">
                            <label for="ratingFilter" class="form-label">Rating minim</label>
                            <select class="form-select" id="ratingFilter" name="min_rating">
                                <option value="">Toate rating-urile</option>
                                <option value="1">⭐ 1+ stele</option>
                                <option value="2">⭐⭐ 2+ stele</option>
                                <option value="3">⭐⭐⭐ 3+ stele</option>
                                <option value="4">⭐⭐⭐⭐ 4+ stele</option>
                                <option value="5">⭐⭐⭐⭐⭐ 5 stele</option>
                            </select>
                        </div>

                        <!-- Facilities Filter -->
                        <div class="mb-3">
                            <label class="form-label">Facilități</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="parcare" id="parkingCheck">
                                <label class="form-check-label" for="parkingCheck">Parcare</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="cazare" id="accommodationCheck">
                                <label class="form-check-label" for="accommodationCheck">Cazare</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="restaurant" id="restaurantCheck">
                                <label class="form-check-label" for="restaurantCheck">Restaurant</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="toalete" id="toiletsCheck">
                                <label class="form-check-label" for="toiletsCheck">Toalete</label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success w-100">Aplică filtre</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-9">
            <!-- Map -->
            <div id="map"></div>

            <!-- Nearby Lakes Section -->
            <div class="nearby-lakes d-none" id="nearbyLakesSection">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3 class="mb-0">Bălți și lacuri în apropiere</h3>
                    <button class="btn btn-outline-success" onclick="requestLocation()">
                        <i class="fas fa-location-crosshairs me-2"></i>Actualizează locația
                    </button>
                </div>
                <div id="nearbyLakesList" class="row">
                    <!-- Nearby lakes will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Location Permission Modal -->
<div class="modal fade location-modal" id="locationModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" onclick="handleLocationDenied()"></button>
            </div>
            <div class="modal-body">
                <i class="fas fa-location-dot location-icon"></i>
                <h4>Permiteți accesul la locație</h4>
                <p>Pentru a vă arăta bălțile și lacurile din apropiere, avem nevoie de permisiunea de a vă accesa locația.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="handleLocationDenied()">Nu acum</button>
                <button type="button" class="btn btn-success" onclick="requestLocation()">Permite accesul</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block external_js %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize map and variables
        var activePopup = null;  // Track currently open popup
        var romaniaCenter = [45.9432, 24.9668];

        // Show location permission modal on page load
        var locationModal = new bootstrap.Modal(document.getElementById('locationModal'));
        var initialMapSetupComplete = false;

        // Handle modal being dismissed without choosing location
        document.getElementById('locationModal').addEventListener('hidden.bs.modal', function () {
            if (!initialMapSetupComplete) {
                showFullRomaniaView();
            }
        });

        locationModal.show();
        var map = L.map('map', {
            zoomControl: true,
            scrollWheelZoom: true,
            keyboard: true,
            doubleClickZoom: true,
            boxZoom: true,
            touchZoom: true,
            minZoom: 6,
            maxZoom: 18,
            dragging: true
        }).setView(romaniaCenter, 7);

        // Set map background color to green - no tile layer needed
        map.getContainer().style.background = '#198754';

        // Load GeoJSON data for Romania's national borders first
        fetch('/static/data/romania-borders.geojson')
            .then(response => {
                if (!response.ok) {
                    console.log('National borders GeoJSON file not found, skipping border display');
                    return null;
                }
                return response.json();
            })
            .then(data => {
                if (!data) return;

                console.log('National borders data loaded successfully');

                // Create the country shape with green fill
                L.geoJSON(data, {
                    style: {
                        color: '#198754',
                        weight: 0,
                        fillColor: '#198754',
                        fillOpacity: 1,
                        opacity: 1
                    },
                    interactive: false
                }).addTo(map);

                // Store Romania bounds for later use
                window.romaniaBounds = L.geoJSON(data).getBounds();

                // Don't set initial view yet - wait for user location choice
                // The view will be set based on location permission response

                // After national borders are loaded, load county boundaries
                return fetch('/static/data/romania-counties.geojson');
            })
            .then(response => {
                if (!response || !response.ok) {
                    console.log('Counties GeoJSON file not found, skipping county borders');
                    return null;
                }
                return response.json();
            })
            .then(countiesData => {
                if (!countiesData) return;

                console.log('Counties data loaded successfully:', countiesData.features.length, 'counties');

                // Add county boundaries with thin white lines on top
                L.geoJSON(countiesData, {
                    style: {
                        color: 'white',
                        weight: 1,
                        fillColor: 'transparent',
                        fillOpacity: 0,
                        opacity: 0.7,
                        lineCap: 'round',
                        lineJoin: 'round'
                    },
                    interactive: false
                }).addTo(map);
            })
            .catch(error => {
                console.log('Error loading GeoJSON data:', error);
            });

        // Price range display
        var priceRange = document.getElementById('priceRange');
        var priceValue = document.getElementById('priceValue');
        priceRange.addEventListener('input', function() {
            priceValue.textContent = this.value;
        });

        // Store all markers in an array
        var markers = [];
        var userLocationMarker = null;

        // Function to show full Romania view
        function showFullRomaniaView() {
            if (window.romaniaBounds) {
                map.fitBounds(window.romaniaBounds, {
                    animate: true,
                    padding: [20, 20]
                });
            } else {
                // Fallback to Romania center if bounds not available
                map.setView(romaniaCenter, 7);
            }
            initialMapSetupComplete = true;
        }

        // Function to handle location permission denied
        window.handleLocationDenied = function() {
            showFullRomaniaView();
        };

        // Function to request location
        window.requestLocation = function() {
            if ("geolocation" in navigator) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const userLat = position.coords.latitude;
                    const userLng = position.coords.longitude;

                    // Remove existing user location marker to prevent duplicates
                    if (userLocationMarker) {
                        map.removeLayer(userLocationMarker);
                        userLocationMarker = null;
                    }

                    // Create custom white pin icon for user location (same shape as lake markers)
                    const userLocationIcon = L.divIcon({
                        className: 'user-location-pin',
                        html: `
                            <svg width="25" height="41" viewBox="0 0 25 41" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12.5 0C5.6 0 0 5.6 0 12.5C0 19.4 12.5 41 12.5 41S25 19.4 25 12.5C25 5.6 19.4 0 12.5 0Z"
                                      fill="white" stroke="#198754" stroke-width="2"/>
                                <circle cx="12.5" cy="12.5" r="6" fill="#198754"/>
                            </svg>
                        `,
                        iconSize: [25, 41],
                        iconAnchor: [12, 41],
                        popupAnchor: [1, -34]
                    });

                    userLocationMarker = L.marker([userLat, userLng], { icon: userLocationIcon })
                        .bindPopup('<div style="text-align: center; font-weight: bold; color: #198754;"><strong>Locația mea</strong></div>')
                        .addTo(map);

                    // Center map on user location with appropriate zoom level
                    map.setView([userLat, userLng], 13, {
                        animate: true
                    });

                    initialMapSetupComplete = true;

                    // Fetch nearby lakes
                    fetch(`/api/nearby-lakes/?lat=${userLat}&lng=${userLng}`)
                        .then(response => response.json())
                        .then(data => {
                            // Show nearby lakes section
                            document.getElementById('nearbyLakesSection').classList.remove('d-none');
                            
                            // Populate nearby lakes
                            const nearbyLakesList = document.getElementById('nearbyLakesList');
                            nearbyLakesList.innerHTML = data.lakes.map(lake => {
                                // Generate rating HTML for nearby lakes
                                let ratingHtml = '';
                                if (lake.total_reviews > 0) {
                                    const fullStars = Math.floor(lake.average_rating);
                                    const hasHalfStar = lake.average_rating % 1 >= 0.5;
                                    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

                                    ratingHtml = `
                                        <div class="rating-info mb-2">
                                            <div class="rating-stars">
                                                ${'<i class="fas fa-star" style="color: #ffc107;"></i>'.repeat(fullStars)}
                                                ${hasHalfStar ? '<i class="fas fa-star-half-alt" style="color: #ffc107;"></i>' : ''}
                                                ${'<i class="far fa-star" style="color: #e9ecef;"></i>'.repeat(emptyStars)}
                                            </div>
                                            <span class="rating-value">${lake.average_rating}</span>
                                            <span>(${lake.total_reviews})</span>
                                        </div>
                                    `;
                                }

                                return `
                                    <div class="col-md-6 mb-3">
                                        <div class="nearby-lake-card">
                                            <h5>${lake.name}</h5>
                                            <p class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>${lake.address}</p>
                                            <p class="mb-2"><i class="fas fa-fish me-2"></i>${lake.fish_species.map(fish => fish.name).join(', ')}</p>
                                            ${ratingHtml}
                                            <p class="distance mb-3"><i class="fas fa-route me-2"></i>${lake.distance} km</p>
                                            <div class="d-flex gap-2">
                                                <a href="/locations/lake/${lake.slug}/" class="btn btn-success flex-grow-1">
                                                    Vezi detalii
                                                </a>
                                                <a href="https://www.google.com/maps/dir/?api=1&destination=${lake.latitude},${lake.longitude}"
                                                   class="btn btn-outline-success" target="_blank">
                                                    <i class="fab fa-google"></i>
                                                </a>
                                                <a href="https://waze.com/ul?ll=${lake.latitude},${lake.longitude}&navigate=yes"
                                                   class="btn btn-outline-success" target="_blank">
                                                    <i class="fab fa-waze"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }).join('');
                        });

                    // Close modal if open
                    const locationModal = bootstrap.Modal.getInstance(document.getElementById('locationModal'));
                    if (locationModal) {
                        locationModal.hide();
                    }
                }, function(error) {
                    // Handle geolocation errors
                    console.error('Geolocation error:', error);
                    alert('Nu am putut obține locația dvs. Vă rugăm să verificați setările de locație din browser.');

                    // Show full Romania view on error
                    showFullRomaniaView();

                    // Close modal if open
                    const locationModal = bootstrap.Modal.getInstance(document.getElementById('locationModal'));
                    if (locationModal) {
                        locationModal.hide();
                    }
                });
            } else {
                alert('Geolocation nu este suportat de acest browser.');

                // Show full Romania view if geolocation not supported
                showFullRomaniaView();

                // Close modal if open
                const locationModal = bootstrap.Modal.getInstance(document.getElementById('locationModal'));
                if (locationModal) {
                    locationModal.hide();
                }
            }
        };

        // Function to add markers to map
        function addMarkers(lakes) {
            // Clear existing markers
            markers.forEach(marker => marker.remove());
            markers = [];

            if (!lakes || lakes.length === 0) {
                return;
            }

            lakes.forEach(lake => {
                // Generate rating stars HTML
                let ratingHtml = '';
                if (lake.total_reviews > 0) {
                    const fullStars = Math.floor(lake.average_rating);
                    const hasHalfStar = lake.average_rating % 1 >= 0.5;
                    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

                    ratingHtml = `
                        <div class="rating-info">
                            <div class="rating-stars">
                                ${'<i class="fas fa-star"></i>'.repeat(fullStars)}
                                ${hasHalfStar ? '<i class="fas fa-star-half-alt"></i>' : ''}
                                ${'<i class="far fa-star"></i>'.repeat(emptyStars)}
                            </div>
                            <span class="rating-value">${lake.average_rating}</span>
                            <span>(${lake.total_reviews} recenzi${lake.total_reviews === 1 ? 'e' : 'i'})</span>
                        </div>
                    `;
                }

                const marker = L.marker([lake.latitude, lake.longitude])
                    .bindPopup(`
                        <div class="lake-popup">
                            <h5>${lake.name}</h5>
                            <p><i class="fas fa-map-marker-alt me-2"></i>${lake.address}</p>
                            <p><i class="fas fa-fish me-2"></i>${lake.fish_species.map(fish => fish.name).join(', ')}</p>
                            ${ratingHtml}
                            <p class="price"><i class="fas fa-tag me-2"></i>${lake.price_per_day} RON/zi</p>
                            <div class="facilities">
                                ${lake.facilities.map(facility => `
                                    <span class="facility">
                                        <i class="${facility.icon_class} me-1"></i>${facility.name}
                                    </span>
                                `).join('')}
                            </div>
                            <a href="/locations/lake/${lake.slug}/" class="btn btn-success">Vezi detalii</a>
                        </div>
                    `);
                marker.addTo(map);
                markers.push(marker);
            });
        }
        
        // Initial markers
        try {
            const lakesData = JSON.parse('{{ lakes_json|escapejs }}');
            addMarkers(lakesData);

            // Only set initial view if user hasn't made a location choice yet
            // This prevents overriding the user's location-based view
            if (!initialMapSetupComplete && lakesData && lakesData.length > 0) {
                if (lakesData.length === 1) {
                    // Single lake - center on it
                    map.setView([lakesData[0].latitude, lakesData[0].longitude], 12);
                } else {
                    // Multiple lakes - fit bounds only if no location choice made
                    const group = new L.featureGroup(markers);
                    map.fitBounds(group.getBounds().pad(0.1));
                }
                initialMapSetupComplete = true;
            }
        } catch (error) {
            console.error('Error parsing lakes data:', error);
        }
        
        // Filter form submission
        document.getElementById('filterForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form values
            const county = document.getElementById('county').value;
            const fishTypes = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                .filter(cb => ['crap', 'caras', 'stiuca', 'salau'].includes(cb.value))
                .map(cb => cb.value);
            const maxPrice = document.getElementById('priceRange').value;
            const facilities = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                .filter(cb => ['parcare', 'cazare', 'restaurant', 'toalete'].includes(cb.value))
                .map(cb => cb.value);
            const minRating = document.getElementById('ratingFilter').value;

            // Build query string
            const params = new URLSearchParams();
            if (county) params.append('county', county);
            fishTypes.forEach(type => params.append('fish_types[]', type));
            if (maxPrice) params.append('max_price', maxPrice);
            facilities.forEach(facility => params.append('facilities[]', facility));
            if (minRating) params.append('min_rating', minRating);
            
            // Fetch filtered lakes
            fetch(`/api/filter-lakes/?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.lakes && Array.isArray(data.lakes)) {
                        addMarkers(data.lakes);
                    } else {
                        console.error('Invalid response format');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('A apărut o eroare la filtrarea lacurilor. Vă rugăm să încercați din nou.');
                });
        });
    });
</script>
{% endblock %}
