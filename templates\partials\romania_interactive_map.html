<!-- Interactive Romania SVG Map -->
<svg id="romaniaMap" baseprofile="tiny" fill="#198754" height="704" stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" version="1.2" viewbox="0 0 1000 704" width="1000" xmlns="http://www.w3.org/2000/svg" class="interactive-svg-map">
 <g id="features">
  <path d="M357.3 75.7l1.5 5.3 4.5 6.5 1 1.1 1.3 1.1 3.3 2.1 1.2 1.2 1 1.8-0.1 1.6-0.8 1.6-13.6 10.4-2.4 1.2-2.2 0.4-1.7-0.1-1.6-0.4-1.5-0.8-3.7-1.1-6.9-0.1-1.5 0.7-1.1 1.2-0.5 2 0.2 1.2 0.6 1 1.1 0.7 6.5 1.8 1.5 0.9 1.1 1 0.7 1.2 0.2 1.9-0.3 1.7-1.2 2.7-0.7 1.8-1.6 3.6-2.1 2.6-1.7 2.5-1.5 1.7-1.8 1-1.6 0-1.5-0.6-4.4-2.9-1.1-1.1-1.6-2-0.8-0.5-0.8 0-1 0.6-5.6 7.2-0.8 1.3-0.5 1.6-0.4 1.5-0.4 1.2-0.5 1-0.8 1-0.9 0.8-3.5 2.4-0.8 0.8-0.3 0.9 0.2 1.1 1.3 1.1 1.7 0.9 8.8 1.7 1.1 2 0.8 1.9 0.2 1.1-0.2 1-0.5 0.9-3.7 3.2-0.6 0.8-0.5 1-0.4 0.6-0.5 0.3-1.2-0.8-1.6-1.9-1.1-0.5-3.9-1.1-1.4-0.8-2.3-2.3-1.4-0.8-10.5-3-1.9 0-2.7 0.7-12.6 6.1-4.1 3.4-0.9-2.5-0.7-3.6-0.9-1.7-1.1-1-2.9-1.6-1.6-1.4-1.3-0.9-1-0.3-1 0.3-2.3 1.3-0.9 0.2-1.4-0.3-0.9 0-0.9 0-0.8 0.3-1 0.2-1.6-0.3-0.8-0.7-0.6-1.1-1.1-3-0.8-1.7-2.8-4.2-1.5-2.9-1.4-2-4-2.9-1.1-1.2-0.8-1.3-1.2-2.8-2-3.1-0.4-0.7 0.1-1 0.1-1 0.3-1 2.4-3.4 0.3-1.2 0.3-2.6 0.4-1.1 0.7-0.9 1.6-1.2 0.7-0.7 2.1-3.2 1.1-1.1 1.8-1 1.7-0.6 1.2-0.2 4.5 0.7 1.4-0.1 1.2-0.5 1.1-1.1 1.6-5.6 3-0.7 7.1 3.7 3.3 0.6 3.7-0.5 3.5-1.6 2.8-2.4 2.4-3.2 1.1-0.9 2-0.9 2.1-0.2 0.5-0.4 0.9-1.6-0.2-1-0.5-1-0.1-1.2 1.1-1.9 1.5-1.1 3.9-1.5 1.5-1.4 2.5-4.5 1.5-1.9 2-0.6 1.5-1 0 0.1 0.1 0 0-0.1 0.1 0 0-0.1 0.1-0.2 0 0.1 0 0.1 0 0.1 0 0.1 0.1 0.1 0.1 0 0.1 0 0-0.1-0.1 0 0-0.1-0.1 0 0-0.1 0.1 0 0.1 0 0 0.1 0.1 0 0-0.1-0.1 0 0-0.1 0.1 0 0.1 0 0.1 0 0-0.1 0.1 0 0 0.1 0.1 0 0 0.1 0.1 0 0.1 0 0.1 0 0.1 0 0.2-0.1 0-0.1 0.1 0 0.2-0.1 0-0.1 0.1 0 0-0.1 0-0.1 0.1 0 0-0.1 0.1 0 0.1 0 0.1 0 0.1 0 0.1 0 0-0.1 0-0.1 0-0.1 0.1 0 0.1 0 0-0.1 0.1 0 0.2-0.1 0.1 0-0.1 0 0-0.1 0-0.1-0.1 0 0 0.1-0.1-0.1-0.1-0.1 0-0.1 0-0.1-0.1-0.1 0-0.1 0-0.1 0-0.1 0-0.1 0-0.1 0-0.1 0-0.1-0.1-0.2 0-0.1 0-0.1 0-0.1-0.1-0.1 0-0.1 0-0.1 0-0.1-0.1 0 0-0.1 0-0.1 0-0.1 0-0.1 0-0.2 0-0.2 0-0.1 0-0.3-0.1-0.1 0-0.1 0-0.1-0.1-0.2 0-0.1 0-0.1-0.1-0.1 0-0.1-0.1-0.1 0-0.1 0-0.1-0.1 0-0.1 0-0.1 0-0.1 0-0.1 0-0.1 0-0.1 0 0-0.1-0.1-0.1-0.1 0 0-0.1-0.1 0 0-0.1-0.1 0-0.1 0 0-0.1 0.1-0.3 1.5-0.1 1.7 0.8 1.5 1 1.5 1 1.5 0.4 1.6-0.2 3.3-2.6 0.2-0.1 0.1-0.1 0.7-0.5 0.4-0.1 0.5 0 0.3 0.1 0.5 0.4 0.1 0 0.1 0 0.1 0 0.2 0 0.1-0.1 0.1 0 0-0.1 0.1-0.1 0-0.1 0.1 0 0-0.1 0-0.2 0-0.1 0.1-0.1 0-0.1 0.1-0.3 0.1-0.1 0.1-0.1 0.1-0.1-0.7-0.1 0-0.1-0.1-0.3 0.3-0.2 0-0.1 0-0.1 0.3-0.1 0.2-0.1 0.1-0.1 0.3-0.2 0-0.2 0.1-0.3 0.3-0.2 0.1-0.3 0.2-0.4 0-0.4-0.2-0.1-0.1-0.1-0.1 0-0.2-0.1-0.2-0.1-0.2-0.4-0.1-0.1 0.4-0.3 0.3-0.2 0.5-0.5 0.2-0.3 0-0.1-0.1-0.2 0-0.1-0.2-0.2 0-0.2 0.3-0.4-0.2 0-0.2-0.2-0.1 0 0-0.3-0.1 0-0.1-0.1 0.1-0.1 0.1 0 0-0.1 0.1 0 0.1 0 0-0.1 0.1 0 0-0.1 0.1-0.1 0.2 0 0.1-0.3 0.3-0.7 0.3 0.1 0.3-0.2-0.2-0.2 0.1-0.2 0.1-0.2-0.1-0.1-0.3-0.2-0.5-0.4 0-0.1 0.1 0 0.1-0.1 0.2 0 0-0.1-0.1 0 0.1-0.1 0-0.1 0-0.1 0.4-0.1 0.4-0.2 0.1 0 0.2-0.4 0.1-0.1 0.5 0 0.1 0.2 0.2 0 0.1-0.4 0.2-0.1 1.9 0.3 6.6 2.3 1.7 1.3 4 4.7 4.6 3.9 2.1 2.6 0.7 0.3 0.7 0.1 0.7-0.1 0.8-0.3 0.1-0.1 0.2 0 0.1 0 0.2 0.1 6.2 3.1 2-0.1 0.1-0.1 0.1 0.1 0.3 0.1z" id="ROSM" name="Satu Mare" class="county-path" data-county="Satu Mare" onmouseover="showCountyName(this)" onmouseout="hideCountyName()" onclick="zoomToCounty(this)">
  </path>
  <path d="M159 261.9l2.1 0.7 4.4 1.1 32.1-1.8 2.5 0.5 0.6 0.9 0.8 0.8 1.3 0.6 1.3 0.2 1.8-0.1 1.2 0.4 1.6 0.8 1.2 1.3 2.4 0.9 8.5 0.4 2.6 0.5 1.8 0.8 1 1 2.2 1.6 0.7 0.9 0.7 1.6 2.1 5.7 1.2 2.3 1.4 1.8 1.9 1.3 2.4 0.7 1.6 0.8 4.5 3.9 7.2 2.7 0.9 1 0.3 1.2 0.4 1.4 1.1 1.6 1.4 0.8 1.5 0.3 8.7 0.2 7.3 1.6 4.6 0.4 4-0.3 4.7 4.8-6.3 4.7-1.7 3.7-0.9 2.2-2.2 3.7-2 1.8-1.8 1-2.3 0.3-1.8-0.1-5.4-1.3-1.7 0.2-1.3 0.8-2.2 2-0.9 1.4-0.4 1.5 0.2 4.6-0.2 1.9-0.4 2.1-1.4 2.9-0.6 1.7-0.2 1.4 1 3.2 0.3 1.5-0.1 1.6-0.7 2.3-1 2.4-4.5 5.8-1.8 0.9-0.3 0.4-0.5 0.5-0.6 2-0.4 0.5-0.6 0.3-1.6 0.2-3.6 0.9-0.8 0.3-0.7 0.7-0.6 0.7-0.6 0.5-0.9-0.1-2.3-1.3-1.3-0.2-2 0.2-1.5 0-1.4-0.3-1-1-0.7-0.8-0.8-0.6-1.6-0.8-0.7-0.5-0.4-0.7-0.3-1-0.5-0.7-1-0.5-3.8-0.3-1.7-0.5-1.2-1.1-0.8-1-1-0.7-1.4-0.5-2.3 0-1.2 0.2-0.7 0.4-0.4 0.7-0.3 0.9-0.6 3.1-0.4 0.7-0.5 0.5-0.8 0.1-2.1 0-1-0.3-0.8-0.8-0.7-0.5-0.5 0-0.5 0.8-0.1 1-0.1 1.1 0.2 1.9-0.2 0.7-0.4 0.2-0.6-0.3-0.8-1-1.2-1.9-0.8-0.8-1.1-0.7-3.1-1.5-1.4 0.1-0.8 0.4-0.7 0.9-1.3 2.3-0.9 2.2-0.6 0.7-0.7 0-0.8-0.8-0.4-0.9-0.6-0.7-1.3-0.9-0.4-0.4-0.1-0.6 0.2-0.7 0.3-0.7 0.1-0.8-0.3-0.9-0.9-1.2-1.9-1.6-2.2-2.2-1-0.6-1-0.3-2.7 0.4-0.9-0.3-0.7-1-0.2-1.1 0-1.1-0.1-0.9-0.8-0.8-1.5-0.5-3.2-0.4-1.6 0.2-1 0.5-0.2 0.8 0 1-0.1 0.9-0.4 0.9-0.6 0.7-2.6 2-2.1 2.2-0.7 0.3-0.6-0.2-1.4-1.6-1-0.5-1.6 0-1.4 0.2-1.1 0.5-2.2 1.9-1.4 0-1.6-0.9-2.5-2.5-0.8-1.7-0.1-1.3-0.2-0.5-0.7-0.3-1.5 0.2-0.9 0.5-0.9 0.8-0.4 0.8-0.2 0.8 0 1.6-0.2 0.7-0.4 0.7-0.7 0.4-1 0.1-1.4-0.3-1.2-0.7-1.3-1.6-0.2-1 0.2-1.6-0.4-0.7-1-0.6-2.1-0.6-1.1 0.1-1.8 0.5-0.9 0-5.9-1.1-1.4-0.5-1.2-0.8-1.3-1.9-1-2.7-3.4-3.5-13.6-9.6-1.4-0.5 0.1-1.7 0.5-1 0.7-0.3 0.9 0.3 0.9 0.2 0.8-0.8 0-1.1-0.9-3.1 0-1.3 0.4-0.8 3.8-3.1 0.8-0.5 1.1-0.6 2-0.5 1.9 0 0.9 0.5 1.8 1.5 0.7 0.4 1-0.1 1.4-0.8 0.7-0.2 1.7 0.4 3.6 1.5 1.9-0.1 0.8-0.3 0.9 0 0.7 0.3 0.7 0.8 1.9 1.7 1.7-0.7 4.6-5.6 0.6-0.3 2.8 0 0.9-0.7 1.1-2.1 0.9-2.7 0.4-6.2 0.9-3 1.6-1.9 1.9-0.7 4.1-0.2 2.2-1.7-0.6-3-2.8-5.4 0.2-2.9 1.4-2.2 1.7-2.1 1.1-2.6 0.4-5.3 0.6-2.7 1.4-1.8 2.1-0.5 3.5 0.3 2.1-1.1 2-2.7 0.7-1.8z" id="ROAR" name="Arad" class="county-path" data-county="Arad" onmouseover="showCountyName(this)" onmouseout="hideCountyName()" onclick="zoomToCounty(this)">
  </path>
  <path d="M230.5 129.6l0.4 0.7 2 3.1 1.2 2.8 0.8 1.3 1.1 1.2 4 2.9 1.4 2 1.5 2.9 2.8 4.2 0.8 1.7 1.1 3 0.6 1.1 0.8 0.7 1.6 0.3 1-0.2 0.8-0.3 0.9 0 0.9 0 1.4 0.3 0.9-0.2 2.3-1.3 1-0.3 1 0.3 1.3 0.9 1.6 1.4 2.9 1.6 1.1 1 0.9 1.7 0.7 3.6 0.9 2.5-1 3.9-0.5 1.2-1.1 1.8-0.5 1.1-0.2 1.2-0.1 1.2-0.3 1.1-0.5 1.1-2.5 3.6-0.8 1-0.8 0.8 0 1.9 1.2 2.9 4.2 6.6 2.8 5.5 0.4 1.1 0.6 1.1 1.1 1.2 6 3.5 3.7 3.8 1.6 2.7-0.7 2-2.1 4.1-0.3 1.3 0.1 0.9 0.9 0.7 4.9 1.9 1.2 1 0.5 1.5-0.4 2.6-0.5 1.6-0.5 1.5-0.5 1.2-0.7 0.9-0.8 0.7-3.6 1.6-1 0.8-0.9 0.9-0.5 1.1-0.2 1.1 0.3 1.3 1.1 1.6 3.9 3.5 1.2 2.1 1.3 2.9 1.6 6.5 0.9 1.7 0.9 0.8 1.1 0.7 0.9 0.9 0.4 1.2-0.4 3.5-0.1 5.2-1.4-0.9-0.9-0.3-1.3-0.2-1.1 0-1.3 0.2-1.1 0.7-1.2 1.4-1.3 2.6-1 3.5-0.5 3.3 0.1 8.5 1.8 7.5-4 0.3-4.6-0.4-7.3-1.6-8.7-0.2-1.5-0.3-1.4-0.8-1.1-1.6-0.4-1.4-0.3-1.2-0.9-1-7.2-2.7-4.5-3.9-1.6-0.8-2.4-0.7-1.9-1.3-1.4-1.8-1.2-2.3-2.1-5.7-0.7-1.6-0.7-0.9-2.2-1.6-1-1-1.8-0.8-2.6-0.5-8.5-0.4-2.4-0.9-1.2-1.3-1.6-0.8-1.2-0.4-1.8 0.1-1.3-0.2-1.3-0.6-0.8-0.8-0.6-0.9-2.5-0.5-32.1 1.8-4.4-1.1-2.1-0.7 0.1-0.5 1.1-1.7 2.6-0.4 1.9-1.1 1.8-2.6 0.3-2.8-2.6-1.8-0.3-0.2-0.2-0.4-0.2-0.4 0.1-0.8 0.2-0.8 0.3-0.8 0.4-0.6 2.1-5.7 1.2-2.3 2-1.9 3.5-0.9 1-0.9 0.8-1.8 0-1.5-0.3-1.5-0.1-1.7 0.1-1.7 0.2-0.4 0.4-0.1 4.4-3.6 0.8-1.1 1.8-6.9 0.4-0.2-0.1-0.1-0.8-1.7-0.7-0.5-0.9-0.2-0.9-0.4-0.2-0.7-0.1-0.5 3.7-4.5 2.2-2.1 4.7-3.1 2-2 0.6-1.2 0.5-2.5 0.4-1.3 0.9-1.3 2.2-2.1 0.8-1.1 0.5-1.7 0-1.3-0.2-1.3 0-1.7 0.4-1.5 1.1-2.1 0.5-1.3 1.1-5.1 0.6-1.6 3.7-5.5 1.8-2 1.7-1 4.3-1.3 1.9-3.9-0.1-4.8-0.8-4.9-0.3-4.4 1.8-3.5 2.9-3.1 5.9-4.5 4.8-1.2 0.5-0.4 0.8-0.6 0.5-1.1 0-0.1z" id="ROBH" name="Bihor" class="county-path" data-county="Bihor" onmouseover="showCountyName(this)" onmouseout="hideCountyName()" onclick="zoomToCounty(this)">
  </path>
  <path d="M89.8 330.2l1.4 0.5 13.6 9.6 3.4 3.5 1 2.7 1.3 1.9 1.2 0.8 1.4 0.5 5.9 1.1 0.9 0 1.8-0.5 1.1-0.1 2.1 0.6 1 0.6 0.4 0.7-0.2 1.6 0.2 1 1.3 1.6 1.2 0.7 1.4 0.3 1-0.1 0.7-0.4 0.4-0.7 0.2-0.7 0-1.6 0.2-0.8 0.4-0.8 0.9-0.8 0.9-0.5 1.5-0.2 0.7 0.3 0.2 0.5 0.1 1.3 0.8 1.7 2.5 2.5 1.6 0.9 1.4 0 2.2-1.9 1.1-0.5 1.4-0.2 1.6 0 1 0.5 1.4 1.6 0.6 0.2 0.7-0.3 2.1-2.2 2.6-2 0.6-0.7 0.4-0.9 0.1-0.9 0-1 0.2-0.8 1-0.5 1.6-0.2 3.2 0.4 1.5 0.5 0.8 0.8 0.1 0.9 0 1.1 0.2 1.1 0.7 1 0.9 0.3 2.7-0.4 1 0.3 1 0.6 2.2 2.2 1.9 1.6 0.9 1.2 0.3 0.9-0.1 0.8-0.3 0.7-0.2 0.7 0.1 0.6 0.4 0.4 1.3 0.9 0.6 0.7 0.4 0.9 0.8 0.8 0.7 0 0.6-0.7 0.9-2.2 1.3-2.3 0.7-0.9 0.8-0.4 1.4-0.1 3.1 1.5 1.1 0.7 0.8 0.8 1.2 1.9 0.8 1 0.6 0.3 0.4-0.2 0.2-0.7-0.2-1.9 0.1-1.1 0.1-1 0.5-0.8 0.5 0 0.7 0.5 0.8 0.8 1 0.3 2.1 0 0.8-0.1 0.5-0.5 0.4-0.7 0.6-3.1 0.3-0.9 0.4-0.7 0.7-0.4 1.2-0.2 2.3 0 1.4 0.5 1 0.7 0.8 1 1.2 1.1 1.7 0.5 3.8 0.3 1 0.5 0.5 0.7 0.3 1 0.4 0.7 0.7 0.5 1.6 0.8 0.8 0.6 0.7 0.8 1 1 1.4 0.3 1.5 0 2-0.2 1.3 0.2 2.3 1.3 0.9 0.1 0.6-0.5 0.6-0.7 0.7-0.7 0.8-0.3 3.6-0.9 1.6-0.2 0.6-0.3 0.4-0.5 0.6-2 0.5-0.5 0.3-0.4 1.8-0.9 3.3 3.5 0.9 2.2 0.3 2.6 0.4 1.7 0.6 2 0.9 1.4 2.7 2.2 0.8 0.8 0.9 1.7 1.8 4.5 0.8 1.2 0.8 0.5 1.2-0.7 0.7-0.2 0.7 0.1 1.1 0.6 0.2 0.9-0.2 1-0.8 1.1-3.9 3.5-1.1 1.2-0.8 1.4-0.4 1.3-0.4 2.6-0.3 1-0.4 0.7-0.3 0.4-0.8 0.4-10.6 2-3.6 2.5-2.7 5.6-0.5 0.4-0.7 0.1-0.8-0.2-0.9-0.1-1.3 0.1-0.9-0.2-0.6-0.3-1.2-1.4-0.7-0.6-1.3-0.2-1 0.4-2.5 2.2-2.4 1.6-2 0.7-2.2 0.4-3.4 0-0.6 0.5-0.1 0.7 0.2 0.9 0.2 1.6 0.2 0.6 0.4 0.4 0.5 0.1 1.3 0.1 0.1 0.5-0.6 0.9-2.5 1.7-1.8 0.7-2 0.3-1.2-0.2-1.1-0.5-0.8-0.7-0.7-1-0.5-1-0.9-2.2-0.7-1.1-0.9-0.8-1.4-0.4-1 0.2-3.4 2.2-4.4 2-2.4 0.6-1.7 0-0.7-0.6-1.2-1.6-1-0.7-1.1-0.5-2.2-0.6-1.8-1.1-1.5-0.4-3 0-2.8 0.9-1.7 1.2-0.9 1.4-0.4 1.5-0.2 1.5 0 1.4-0.7 1.3-1.3 1.4-4.1 2.3-1.7 1.5-0.9 1.3 0 1.1 0.2 1.4 0 1.2-0.4 1.2-1 2-0.3 1.1 0.1 1.1 0.3 1.2 0.6 1.1 3.3 3.4 1.9 1.6 0.7 0.8 0.5 1 0.3 1 0.1 1-0.1 0.8 0.1 0.8 0.3 1.3-0.1 0.8-0.3 0.8-1.9 2.4-0.3 1.1 0 1.4 0.5 3.8 0 1.3-0.5 1.4-0.5 1.1-1.7 2.3-1.9 1-2.4-2.9-2.5-2-2.7-1.5-10.2-3.2-4-0.1-1.8-0.7-3.1-2.3-1.6-1.9-3.3-4.8-1.6-1.2-0.9 0.3-1.6 1.7-0.9 0.4-1.2-0.3-0.8-0.7-0.8-1-1-0.7-4.6-2.1-3.3-1.6-1.5-1.2-3.7-4.9-6.2-5.6-3.2-4.7-1.3-1.4-1.6-0.7-1.8-0.6-1.3-0.9-0.7-1.9 2.2-1.8 1.4-1.4 0.2-1.9-1.2-3.2-2.8-4.9-0.4-2.3 0.8-3.4 1-2.5 0.4-1.2 0.3-1.9-0.1-7.2 0.2-1.4 0.4-1.3 0-1.3-0.7-1.3-1.2-0.6-1.1 0.4-0.8 1.2-0.6 1.6-1.2 1-1.3 0.4-1.3-0.3-1.1-1.1-1-1.8-2.2-2.9-0.9-1.6-0.2-0.8-0.1-0.5-0.3-2.8-0.3-1.1-0.7-0.9-1.6-1.1-0.7-0.7-3.2-5.7-1.5-1.5-1.8-0.7-3.6-0.4-1.8-0.9-5-4.7-1.8-1.2-3.8-1.7-1.7-1.2-1.4-2.2-2.1-6.4-1.1-2.1-6-7.5 3.8-5 15.5-0.4 2.4-3.8 3.9 0.9 3.8 1.6 2.8 2.6 0.9 0.6 1.2 0.5 0.7 0 5.4-1.1 1.9-1 1.5-1.6 0.5-1.4 0-0.3z" id="ROTM" name="Timis" class="county-path" data-county="Timiș" onmouseover="showCountyName(this)" onmouseout="hideCountyName()" onclick="zoomToCounty(this)">
  </path>
 </g>
</svg>

<style>
/* Interactive Romania Map Styles */
.interactive-romania-map {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.county-name-display {
    background: rgba(25, 135, 84, 0.9);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.svg-map-container {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    background: #f8f9fa;
}

.interactive-svg-map {
    width: 100%;
    height: auto;
    transition: transform 0.5s ease;
    transform-origin: center center;
}

.county-path {
    fill: #198754;
    stroke: white;
    stroke-width: 1;
    cursor: pointer;
    transition: all 0.3s ease;
}

.county-path:hover {
    fill: white;
    stroke: #198754;
    stroke-width: 2;
}

.county-path.zoomed {
    fill: #198754;
    stroke: white;
    stroke-width: 2;
}

.map-controls {
    text-align: center;
}

@media (max-width: 768px) {
    .interactive-romania-map {
        max-width: 100%;
    }
    
    .county-name-display {
        font-size: 1rem;
        padding: 10px 16px;
        margin-bottom: 15px;
    }
    
    .svg-map-container {
        border-radius: 8px;
    }
}
</style>

<script>
// Interactive Romania Map JavaScript
let currentZoomedCounty = null;
let originalViewBox = "0 0 1000 704";

function showCountyName(element) {
    const countyName = element.getAttribute('data-county');
    const display = document.getElementById('countyNameDisplay');
    display.textContent = `Județ: ${countyName}`;
    display.style.background = 'rgba(25, 135, 84, 1)';
}

function hideCountyName() {
    const display = document.getElementById('countyNameDisplay');
    if (!currentZoomedCounty) {
        display.textContent = 'Selectează un județ pentru a vedea lacurile';
        display.style.background = 'rgba(25, 135, 84, 0.9)';
    }
}

function zoomToCounty(element) {
    const svg = document.getElementById('romaniaMap');
    const resetBtn = document.getElementById('resetZoomBtn');
    const countyName = element.getAttribute('data-county');
    
    // Reset previous county
    if (currentZoomedCounty) {
        currentZoomedCounty.classList.remove('zoomed');
    }
    
    // Get bounding box of the county path
    const bbox = element.getBBox();
    
    // Calculate zoom parameters
    const padding = 50;
    const newViewBox = `${bbox.x - padding} ${bbox.y - padding} ${bbox.width + 2 * padding} ${bbox.height + 2 * padding}`;
    
    // Apply zoom
    svg.setAttribute('viewBox', newViewBox);
    element.classList.add('zoomed');
    currentZoomedCounty = element;
    
    // Update display
    const display = document.getElementById('countyNameDisplay');
    display.textContent = `Județ: ${countyName} (Zoom activ)`;
    display.style.background = 'rgba(25, 135, 84, 1)';
    
    // Show reset button
    resetBtn.style.display = 'inline-block';
    
    // Optional: Navigate to county lakes page after a delay
    setTimeout(() => {
        // You can add navigation logic here if needed
        // window.location.href = `/locations/county/${countySlug}/`;
    }, 1000);
}

function resetMapZoom() {
    const svg = document.getElementById('romaniaMap');
    const resetBtn = document.getElementById('resetZoomBtn');
    const display = document.getElementById('countyNameDisplay');
    
    // Reset zoom
    svg.setAttribute('viewBox', originalViewBox);
    
    // Reset county styling
    if (currentZoomedCounty) {
        currentZoomedCounty.classList.remove('zoomed');
        currentZoomedCounty = null;
    }
    
    // Reset display
    display.textContent = 'Selectează un județ pentru a vedea lacurile';
    display.style.background = 'rgba(25, 135, 84, 0.9)';
    
    // Hide reset button
    resetBtn.style.display = 'none';
}

// Touch/swipe support for mobile
let touchStartX = 0;
let touchStartY = 0;

document.addEventListener('touchstart', function(e) {
    touchStartX = e.touches[0].clientX;
    touchStartY = e.touches[0].clientY;
});

document.addEventListener('touchend', function(e) {
    if (!currentZoomedCounty) return;
    
    const touchEndX = e.changedTouches[0].clientX;
    const touchEndY = e.changedTouches[0].clientY;
    const diffX = touchStartX - touchEndX;
    const diffY = touchStartY - touchEndY;
    
    // If significant swipe detected, reset zoom
    if (Math.abs(diffX) > 50 || Math.abs(diffY) > 50) {
        resetMapZoom();
    }
});
</script>
