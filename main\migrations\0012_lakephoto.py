# Generated by Django 5.1.6 on 2025-06-11 18:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0011_auto_20250605_2315'),
    ]

    operations = [
        migrations.CreateModel(
            name='LakePhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(help_text='Fotografia lacului (format recomandat: JPG, PNG, max 2MB)', upload_to='lakes/gallery/', verbose_name='Fotografie')),
                ('caption', models.CharField(blank=True, help_text='Descrierea opțională a fotografiei (ex: Vedere de ansamblu, Zona de pescuit, etc.)', max_length=200, verbose_name='Descriere')),
                ('order', models.PositiveIntegerField(default=0, help_text='Ordinea de afișare a fotografiei (0 = prima fotografie)', verbose_name='Ordine')),
                ('is_main', models.BooleanField(default=False, help_text='Marchează această fotografie ca fiind principală pentru lac', verbose_name='Fotografie principală')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data adăugării')),
                ('lake', models.ForeignKey(help_text='Lacul căruia îi aparține această fotografie', on_delete=django.db.models.deletion.CASCADE, related_name='photos', to='main.lake', verbose_name='Lac')),
            ],
            options={
                'verbose_name': 'Fotografie lac',
                'verbose_name_plural': 'Fotografii lacuri',
                'ordering': ['lake', 'order'],
                'unique_together': {('lake', 'order')},
            },
        ),
    ]
